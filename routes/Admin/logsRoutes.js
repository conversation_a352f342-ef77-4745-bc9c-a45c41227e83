/**
 * Admin Logs Routes
 * Routes for accessing and managing application logs
 */

const express = require('express');
const router = express.Router();
const logsController = require('../../controllers/Admin/LogsController');
const authentication = require('../../middlewares/authentication');

/**
 * @route GET /admin/logs
 * @desc Get logs with filtering and search capabilities
 * @access Admin only
 * @query {string} page - Page number (default: 1)
 * @query {string} limit - Records per page (default: 50)
 * @query {string} start_date - Start date filter (YYYY-MM-DD)
 * @query {string} end_date - End date filter (YYYY-MM-DD)
 * @query {string|array} action_type - Filter by action type(s)
 * @query {string} platform - Filter by platform (APP/CMS)
 * @query {string} user_name - Search by user name
 * @query {string} company_name - Search by company name
 * @query {string} shipment_number - Search by shipment number
 * @query {string} item_name - Search by item name
 * @query {string} user_type - Filter by user type (staff/admin/customer)
 * @query {string} sort_by - Sort field (default: created_at)
 * @query {string} sort_order - Sort order (ASC/DESC, default: DESC)
 */
router.get('/', 
  authentication.validateAdminToken,
  logsController.getLogsController
);

/**
 * @route GET /admin/logs/stats
 * @desc Get log statistics and summary
 * @access Admin only
 * @query {string} start_date - Start date filter (YYYY-MM-DD)
 * @query {string} end_date - End date filter (YYYY-MM-DD)
 * @query {string} company_id - Filter by company ID
 */
router.get('/stats',
  authentication.validateAdminToken,
  logsController.getLogStatsController
);

/**
 * @route GET /admin/logs/export
 * @desc Export logs to CSV
 * @access Admin only
 * @query {string} start_date - Start date filter (YYYY-MM-DD)
 * @query {string} end_date - End date filter (YYYY-MM-DD)
 * @query {string} action_type - Filter by action type
 * @query {string} platform - Filter by platform (APP/CMS)
 * @query {string} company_id - Filter by company ID
 */
router.get('/export',
  authentication.validateAdminToken,
  logsController.exportLogsController
);

module.exports = router;
