<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Logs - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .log-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .log-entry {
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log-entry.error { border-left-color: #dc3545; }
        .log-entry.warning { border-left-color: #ffc107; }
        .log-entry.success { border-left-color: #28a745; }
        .action-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }
        .user-info {
            color: #6c757d;
            font-size: 0.9em;
        }
        .log-details {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-list-alt"></i> Application Logs</h2>
                    <div>
                        <button class="btn btn-success" onclick="exportLogs()">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                        <button class="btn btn-info" onclick="refreshLogs()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5>Total Logs Today</h5>
                            <h3 id="totalLogsToday">-</h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5>Active Users</h5>
                            <h3 id="activeUsers">-</h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5>Items Processed</h5>
                            <h3 id="itemsProcessed">-</h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5>Error Rate</h5>
                            <h3 id="errorRate">-</h3>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="log-filters">
                    <form id="filterForm">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">Date Range</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="startDate" name="start_date">
                                    <input type="date" class="form-control" id="endDate" name="end_date">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Platform</label>
                                <select class="form-select" id="platform" name="platform">
                                    <option value="">All Platforms</option>
                                    <option value="APP">Mobile APP</option>
                                    <option value="CMS">CMS</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Action Type</label>
                                <select class="form-select" id="actionType" name="action_type">
                                    <option value="">All Actions</option>
                                    <option value="LOGIN">Login</option>
                                    <option value="STAGE_ACCESS">Stage Access</option>
                                    <option value="ITEM_ADD">Item Add</option>
                                    <option value="ITEM_EDIT">Item Edit</option>
                                    <option value="ITEM_DELETE">Item Delete</option>
                                    <option value="ITEM_DUPLICATE">Item Duplicate</option>
                                    <option value="SCAN_QR">QR Scan</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">User Type</label>
                                <select class="form-select" id="userType" name="user_type">
                                    <option value="">All Users</option>
                                    <option value="staff">Staff</option>
                                    <option value="admin">Admin</option>
                                    <option value="customer">Customer</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchTerm" placeholder="User name, company, shipment...">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <input type="text" class="form-control" name="user_name" placeholder="Search by user name">
                            </div>
                            <div class="col-md-3">
                                <input type="text" class="form-control" name="company_name" placeholder="Search by company">
                            </div>
                            <div class="col-md-3">
                                <input type="text" class="form-control" name="shipment_number" placeholder="Search by shipment">
                            </div>
                            <div class="col-md-3">
                                <input type="text" class="form-control" name="item_name" placeholder="Search by item name">
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Logs Display -->
                <div id="logsContainer">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <nav aria-label="Logs pagination" id="paginationContainer" style="display: none;">
                    <ul class="pagination justify-content-center" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let currentFilters = {};

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs();
            loadStats();
            
            // Set default date range (last 7 days)
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
        });

        // Filter form submission
        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            currentPage = 1;
            loadLogs();
        });

        // Load logs function
        async function loadLogs(page = 1) {
            try {
                const formData = new FormData(document.getElementById('filterForm'));
                const params = new URLSearchParams();
                
                for (let [key, value] of formData.entries()) {
                    if (value) params.append(key, value);
                }
                
                params.append('page', page);
                params.append('limit', 20);

                const response = await fetch(`/admin/logs?${params.toString()}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
                    }
                });

                const data = await response.json();
                
                if (data.status === 1) {
                    displayLogs(data.data.logs);
                    displayPagination(data.data.pagination);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('Error loading logs:', error);
                document.getElementById('logsContainer').innerHTML = 
                    `<div class="alert alert-danger">Error loading logs: ${error.message}</div>`;
            }
        }

        // Display logs
        function displayLogs(logs) {
            const container = document.getElementById('logsContainer');
            
            if (logs.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No logs found for the selected criteria.</div>';
                return;
            }

            const logsHtml = logs.map(log => {
                const actionClass = getActionClass(log.action_type);
                const userInfo = log.user_info ? 
                    `${log.user_info.name} (${log.user_info.type})` : 'Unknown User';
                
                return `
                    <div class="log-entry ${actionClass}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-primary action-badge me-2">${log.action_type}</span>
                                    <span class="badge bg-secondary action-badge me-2">${log.platform}</span>
                                    <small class="text-muted">${new Date(log.created_at).toLocaleString()}</small>
                                </div>
                                <h6 class="mb-1">${log.action_description}</h6>
                                <div class="user-info">
                                    <i class="fas fa-user"></i> ${userInfo}
                                    ${log.company_info ? `| <i class="fas fa-building"></i> ${log.company_info.name}` : ''}
                                    ${log.shipment_info ? `| <i class="fas fa-truck"></i> ${log.shipment_info.job_number}` : ''}
                                </div>
                            </div>
                            <button class="btn btn-sm btn-outline-info" onclick="toggleDetails(${log.log_id})">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                        <div id="details-${log.log_id}" class="log-details" style="display: none;">
                            ${log.item_info ? `<p><strong>Item:</strong> ${log.item_info.name}</p>` : ''}
                            ${log.item_count ? `<p><strong>Item Count:</strong> ${log.item_count}</p>` : ''}
                            ${log.interaction_method ? `<p><strong>Method:</strong> ${log.interaction_method}</p>` : ''}
                            ${log.modified_fields ? `<p><strong>Modified Fields:</strong> <pre>${JSON.stringify(log.modified_fields, null, 2)}</pre></p>` : ''}
                            ${log.ip_address ? `<p><strong>IP Address:</strong> ${log.ip_address}</p>` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = logsHtml;
        }

        // Get CSS class for action type
        function getActionClass(actionType) {
            switch(actionType) {
                case 'ERROR': return 'error';
                case 'ITEM_DELETE': return 'warning';
                case 'LOGIN': case 'ITEM_ADD': return 'success';
                default: return '';
            }
        }

        // Toggle log details
        function toggleDetails(logId) {
            const details = document.getElementById(`details-${logId}`);
            details.style.display = details.style.display === 'none' ? 'block' : 'none';
        }

        // Display pagination
        function displayPagination(pagination) {
            const container = document.getElementById('paginationContainer');
            const paginationEl = document.getElementById('pagination');
            
            if (pagination.total_pages <= 1) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            
            let paginationHtml = '';
            
            // Previous button
            paginationHtml += `
                <li class="page-item ${pagination.current_page === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadLogs(${pagination.current_page - 1})">Previous</a>
                </li>
            `;
            
            // Page numbers
            for (let i = 1; i <= pagination.total_pages; i++) {
                if (i === pagination.current_page || 
                    i === 1 || 
                    i === pagination.total_pages || 
                    (i >= pagination.current_page - 2 && i <= pagination.current_page + 2)) {
                    paginationHtml += `
                        <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="loadLogs(${i})">${i}</a>
                        </li>
                    `;
                } else if (i === pagination.current_page - 3 || i === pagination.current_page + 3) {
                    paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
            }
            
            // Next button
            paginationHtml += `
                <li class="page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadLogs(${pagination.current_page + 1})">Next</a>
                </li>
            `;
            
            paginationEl.innerHTML = paginationHtml;
        }

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/admin/logs/stats', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
                    }
                });

                const data = await response.json();
                
                if (data.status === 1) {
                    // Update stats display
                    // This would need to be implemented based on your stats structure
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Export logs
        async function exportLogs() {
            try {
                const formData = new FormData(document.getElementById('filterForm'));
                const params = new URLSearchParams();
                
                for (let [key, value] of formData.entries()) {
                    if (value) params.append(key, value);
                }

                window.open(`/admin/logs/export?${params.toString()}`, '_blank');
            } catch (error) {
                console.error('Error exporting logs:', error);
                alert('Error exporting logs');
            }
        }

        // Refresh logs
        function refreshLogs() {
            loadLogs(currentPage);
            loadStats();
        }
    </script>
</body>
</html>
