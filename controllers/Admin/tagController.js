const tagModel = require("../../models/Admin/tagModel");
const commonFunction = require("../../assets/common");
const commonModel = require("../../models/Admin/commonModel");
const ActionLogModel = require("../../models/Admin/ActionLogModel");


exports.getTagListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagListing = await tagModel.getTagListingModel(request.query, getUserDetails)
		if (tagListing)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.getTagListingController -> error: ", reason);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.getTagForCustomerListingController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagListing = await tagModel.getTagListingForCustomerModel(getUserDetails)
		if (tagListing)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.getTagForCustomerListingController -> error: ", reason);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.getTagForShipmentListingController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagListing = await tagModel.getTagListingForShipmentModel(getUserDetails)
		if (tagListing)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.getTagForShipmentListingController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.createTagController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagDetails = await tagModel.createTagModel(request.body, getUserDetails)
		if (tagDetails) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				staff_id: getUserDetails.staff_id,
				company_id: getUserDetails.staff_id ? null : getUserDetails.company_id,
				admin_id: getUserDetails.admin_id,
				tag_id: tagDetails.tag_id,
				action_type: "TAG_ADD",
				created_by: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				updated_by: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
			});
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_ADDED_SUCCESS, tagDetails)
		}
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_ADDED_FAILURE, {})
	}
	catch (reason) {
		console.log("exports.createTagController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}
exports.editTagController = async (request, response) => {
	try {
		const { tagId } = request.params
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagDetails = await tagModel.editTagModel(tagId, request.body, getUserDetails)
		if (tagDetails) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				staff_id: getUserDetails.staff_id,
				company_id: getUserDetails.staff_id ? null : getUserDetails.company_id,
				admin_id: getUserDetails.admin_id,
				tag_id: tagId,
				action_type: "TAG_EDIT",
				created_by: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				updated_by: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
			});
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_EDIT_SUCCESS, tagDetails)
		}
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_EDIT_FAILURE, {})
	}
	catch (reason) {
		console.log("exports.editTagController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.removeTagController = async (request, response) => {
	try {
		const { tagId } = request.params
		const data = await tagModel.removeTagModel(tagId)
		let getUserDetails = await commonModel.getUserDetails(request);
		if (data) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				staff_id: getUserDetails.staff_id,
				company_id: getUserDetails.staff_id ? null : getUserDetails.company_id,
				admin_id: getUserDetails.admin_id,
				tag_id: tagId,
				action_type: "TAG_DELETE",
				created_by: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				updated_by: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
			});
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_DELETE_SUCCESS, {})
		}
	}
	catch (reason) {
		console.log("exports.removeTagController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.isValidTagController = async (request, response, next) => {
	try {
		const tag = request.body ? (request.body.tag ? request.body.tag : []) : []
		if (request.params.tagId)
			tag.push(request.params.tagId ? request.params.tagId : request.body.tag_id)
		//newChanges
		tag.map(async (tagId) => {
			const isValidTag = await tagModel.checkTagExistenceModel(tagId);
			if (!isValidTag)
				return commonFunction.generateResponse(response, NOT_FOUND_CODE, 0, TAG_NOT_FOUND, {})
		})
		next()
	} catch (error) {
		console.log("exports.isValidTagController -> error: ", error);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {})
	}
}

exports.isTagAssignToItemController = async (request, response, next) => {
	try {
		const { type } = request.body;
		console.log("🚀 ~ type:", type)
		const { tagId } = request.params
		console.log("🚀 ~ tagId:", tagId)
		const { count } = await tagModel.checkTagAssignModel(tagId, type)
		console.log("🚀 ~ count:", count)
		if (count && count > 0) {
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, "Cannot delete this tag as this tag is assign to item", {});
		} else {
			next();
		}
	} catch (error) {
		console.log("exports.isTagAssignToItemController -> error: ", error);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {})
	}
}

exports.viewTagController = async (request, response) => {
	try {
		const { tagId } = request.params
		const tagDetails = await tagModel.getTagModel(tagId)
		if (tagDetails)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_DETAILS, tagDetails)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.viewTagController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}
