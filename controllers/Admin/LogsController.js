/**
 * Admin Logs Controller
 * Provides comprehensive log viewing and filtering for administrators
 */

const { fetchActionLogWithFilters } = require('../../models/Admin/ActionLogModel');
const { app_logs, staff, admin, customer, company, shipment_job, shipment_inventory, unit_list } = require('../../database/schemas');
const { Op, fn, col, literal } = require('sequelize');
const commonFunction = require('../../assets/common');
const {
  SUCCESS_CODE,
  SERVER_ERROR_CODE,
  NOT_FOUND_CODE,
  NOT_VALID_DATA_CODE,
} = require('../../assets/statusCode');

/**
 * Get logs with advanced filtering and search capabilities
 */
exports.getLogsController = async (request, response) => {
  try {
    const {
      page = 1,
      limit = 50,
      start_date,
      end_date,
      action_type,
      platform,
      user_name, // Search by user name
      company_name, // Search by company name
      shipment_number, // Search by shipment job number
      item_name, // Search by item name
      user_type, // staff, admin, customer
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = request.query;

    // Build where conditions
    const whereConditions = {};
    const includeConditions = [];

    // Date range filter
    if (start_date && end_date) {
      whereConditions.created_at = {
        [Op.between]: [start_date, end_date]
      };
    } else if (start_date) {
      whereConditions.created_at = {
        [Op.gte]: start_date
      };
    } else if (end_date) {
      whereConditions.created_at = {
        [Op.lte]: end_date
      };
    }

    // Action type filter
    if (action_type) {
      if (Array.isArray(action_type)) {
        whereConditions.action_type = { [Op.in]: action_type };
      } else {
        whereConditions.action_type = action_type;
      }
    }

    // Platform filter
    if (platform) {
      whereConditions.platform = platform;
    }

    // Build includes with search conditions
    const staffInclude = {
      model: staff,
      as: 'staff_user',
      attributes: ['staff_id', 'first_name', 'last_name', 'email'],
      required: false
    };

    const adminInclude = {
      model: admin,
      as: 'admin_user',
      attributes: ['admin_id', 'first_name', 'last_name', 'email'],
      required: false
    };

    const customerInclude = {
      model: customer,
      as: 'customer_user',
      attributes: ['customer_id', 'first_name', 'last_name', 'email'],
      required: false
    };

    const companyInclude = {
      model: company,
      as: 'company',
      attributes: ['company_id', 'company_name'],
      required: false
    };

    const shipmentInclude = {
      model: shipment_job,
      as: 'shipment',
      attributes: ['shipment_job_id', 'job_number', 'shipment_name'],
      required: false
    };

    const inventoryInclude = {
      model: shipment_inventory,
      as: 'inventory',
      attributes: ['shipment_inventory_id', 'item_name'],
      required: false
    };

    // Add search conditions for user names
    if (user_name) {
      const nameSearch = `%${user_name}%`;
      
      if (user_type === 'staff') {
        staffInclude.where = {
          [Op.or]: [
            { first_name: { [Op.like]: nameSearch } },
            { last_name: { [Op.like]: nameSearch } },
            literal(`CONCAT(first_name, ' ', last_name) LIKE '${nameSearch}'`)
          ]
        };
        staffInclude.required = true;
      } else if (user_type === 'admin') {
        adminInclude.where = {
          [Op.or]: [
            { first_name: { [Op.like]: nameSearch } },
            { last_name: { [Op.like]: nameSearch } },
            literal(`CONCAT(first_name, ' ', last_name) LIKE '${nameSearch}'`)
          ]
        };
        adminInclude.required = true;
      } else if (user_type === 'customer') {
        customerInclude.where = {
          [Op.or]: [
            { first_name: { [Op.like]: nameSearch } },
            { last_name: { [Op.like]: nameSearch } },
            literal(`CONCAT(first_name, ' ', last_name) LIKE '${nameSearch}'`)
          ]
        };
        customerInclude.required = true;
      } else {
        // Search across all user types
        const userSearchCondition = {
          [Op.or]: [
            { '$staff_user.first_name$': { [Op.like]: nameSearch } },
            { '$staff_user.last_name$': { [Op.like]: nameSearch } },
            { '$admin_user.first_name$': { [Op.like]: nameSearch } },
            { '$admin_user.last_name$': { [Op.like]: nameSearch } },
            { '$customer_user.first_name$': { [Op.like]: nameSearch } },
            { '$customer_user.last_name$': { [Op.like]: nameSearch } }
          ]
        };
        Object.assign(whereConditions, userSearchCondition);
      }
    }

    // Add search condition for company name
    if (company_name) {
      companyInclude.where = {
        company_name: { [Op.like]: `%${company_name}%` }
      };
      companyInclude.required = true;
    }

    // Add search condition for shipment
    if (shipment_number) {
      shipmentInclude.where = {
        [Op.or]: [
          { job_number: { [Op.like]: `%${shipment_number}%` } },
          { shipment_name: { [Op.like]: `%${shipment_number}%` } }
        ]
      };
      shipmentInclude.required = true;
    }

    // Add search condition for item name
    if (item_name) {
      inventoryInclude.where = {
        item_name: { [Op.like]: `%${item_name}%` }
      };
      inventoryInclude.required = true;
    }

    // Build final query
    const queryOptions = {
      where: whereConditions,
      include: [
        staffInclude,
        adminInclude,
        customerInclude,
        companyInclude,
        shipmentInclude,
        inventoryInclude
      ],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      order: [[sort_by, sort_order.toUpperCase()]],
      distinct: true
    };

    const logs = await app_logs.findAndCountAll(queryOptions);

    // Format the response
    const formattedLogs = logs.rows.map(log => {
      const logData = log.toJSON();
      
      // Determine user info
      let user_info = null;
      if (logData.staff_user) {
        user_info = {
          type: 'staff',
          id: logData.staff_user.staff_id,
          name: `${logData.staff_user.first_name} ${logData.staff_user.last_name}`,
          email: logData.staff_user.email
        };
      } else if (logData.admin_user) {
        user_info = {
          type: 'admin',
          id: logData.admin_user.admin_id,
          name: `${logData.admin_user.first_name} ${logData.admin_user.last_name}`,
          email: logData.admin_user.email
        };
      } else if (logData.customer_user) {
        user_info = {
          type: 'customer',
          id: logData.customer_user.customer_id,
          name: `${logData.customer_user.first_name} ${logData.customer_user.last_name}`,
          email: logData.customer_user.email
        };
      }

      return {
        log_id: logData.log_id,
        platform: logData.platform,
        action_type: logData.action_type,
        action_description: logData.action_description,
        user_info: user_info,
        company_info: logData.company ? {
          id: logData.company.company_id,
          name: logData.company.company_name
        } : null,
        shipment_info: logData.shipment ? {
          id: logData.shipment.shipment_job_id,
          job_number: logData.shipment.job_number,
          name: logData.shipment.shipment_name
        } : null,
        item_info: logData.inventory ? {
          id: logData.inventory.shipment_inventory_id,
          name: logData.inventory.item_name
        } : null,
        item_count: logData.item_count,
        interaction_method: logData.interaction_method,
        modified_fields: logData.modified_fields,
        ip_address: logData.ip_address,
        session_id: logData.session_id,
        created_at: logData.created_at
      };
    });

    response.status(SUCCESS_CODE).json({
      status: 1,
      message: 'Logs retrieved successfully',
      data: {
        logs: formattedLogs,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(logs.count / parseInt(limit)),
          total_records: logs.count,
          per_page: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching logs:', error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {}
    });
  }
};

/**
 * Get log statistics and summary
 */
exports.getLogStatsController = async (request, response) => {
  try {
    const { start_date, end_date, company_id } = request.query;

    const whereConditions = {};

    if (company_id) {
      whereConditions.company_id = company_id;
    }

    if (start_date && end_date) {
      whereConditions.created_at = {
        [Op.between]: [start_date, end_date]
      };
    }

    // Get action type statistics
    const actionStats = await app_logs.findAll({
      where: whereConditions,
      attributes: [
        'action_type',
        [fn('COUNT', col('log_id')), 'count'],
        [fn('SUM', col('item_count')), 'total_items']
      ],
      group: ['action_type'],
      order: [[fn('COUNT', col('log_id')), 'DESC']]
    });

    // Get user activity statistics
    const userStats = await app_logs.findAll({
      where: whereConditions,
      attributes: [
        'staff_id',
        [fn('COUNT', col('log_id')), 'activity_count'],
        [fn('SUM', col('item_count')), 'total_items']
      ],
      include: [{
        model: staff,
        as: 'staff_user',
        attributes: ['first_name', 'last_name'],
        required: false
      }],
      group: ['staff_id'],
      order: [[fn('COUNT', col('log_id')), 'DESC']],
      limit: 10
    });

    // Get platform statistics
    const platformStats = await app_logs.findAll({
      where: whereConditions,
      attributes: [
        'platform',
        [fn('COUNT', col('log_id')), 'count']
      ],
      group: ['platform']
    });

    // Get daily activity for the last 7 days
    const dailyActivity = await app_logs.findAll({
      where: {
        ...whereConditions,
        created_at: {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      },
      attributes: [
        [fn('DATE', col('created_at')), 'date'],
        [fn('COUNT', col('log_id')), 'count']
      ],
      group: [fn('DATE', col('created_at'))],
      order: [[fn('DATE', col('created_at')), 'ASC']]
    });

    response.status(SUCCESS_CODE).json({
      status: 1,
      message: 'Log statistics retrieved successfully',
      data: {
        action_statistics: actionStats,
        user_activity: userStats,
        platform_statistics: platformStats,
        daily_activity: dailyActivity
      }
    });

  } catch (error) {
    console.error('Error fetching log stats:', error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {}
    });
  }
};

/**
 * Export logs to CSV
 */
exports.exportLogsController = async (request, response) => {
  try {
    const {
      start_date,
      end_date,
      action_type,
      platform,
      company_id
    } = request.query;

    const whereConditions = {};

    if (start_date && end_date) {
      whereConditions.created_at = {
        [Op.between]: [start_date, end_date]
      };
    }

    if (action_type) {
      whereConditions.action_type = action_type;
    }

    if (platform) {
      whereConditions.platform = platform;
    }

    if (company_id) {
      whereConditions.company_id = company_id;
    }

    const logs = await app_logs.findAll({
      where: whereConditions,
      include: [
        {
          model: staff,
          as: 'staff_user',
          attributes: ['first_name', 'last_name', 'email'],
          required: false
        },
        {
          model: admin,
          as: 'admin_user',
          attributes: ['first_name', 'last_name', 'email'],
          required: false
        },
        {
          model: customer,
          as: 'customer_user',
          attributes: ['first_name', 'last_name', 'email'],
          required: false
        },
        {
          model: company,
          as: 'company',
          attributes: ['company_name'],
          required: false
        },
        {
          model: shipment_job,
          as: 'shipment',
          attributes: ['job_number', 'shipment_name'],
          required: false
        },
        {
          model: shipment_inventory,
          as: 'inventory',
          attributes: ['item_name'],
          required: false
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Convert to CSV format
    const csvHeaders = [
      'Log ID',
      'Date/Time',
      'Platform',
      'Action Type',
      'Description',
      'User Name',
      'User Type',
      'Company',
      'Shipment',
      'Item',
      'Item Count',
      'Interaction Method',
      'IP Address'
    ];

    const csvRows = logs.map(log => {
      let userName = 'Unknown';
      let userType = 'Unknown';

      if (log.staff_user) {
        userName = `${log.staff_user.first_name} ${log.staff_user.last_name}`;
        userType = 'Staff';
      } else if (log.admin_user) {
        userName = `${log.admin_user.first_name} ${log.admin_user.last_name}`;
        userType = 'Admin';
      } else if (log.customer_user) {
        userName = `${log.customer_user.first_name} ${log.customer_user.last_name}`;
        userType = 'Customer';
      }

      return [
        log.log_id,
        log.created_at,
        log.platform,
        log.action_type,
        log.action_description,
        userName,
        userType,
        log.company ? log.company.company_name : '',
        log.shipment ? log.shipment.job_number : '',
        log.inventory ? log.inventory.item_name : '',
        log.item_count || '',
        log.interaction_method || '',
        log.ip_address || ''
      ];
    });

    const csvContent = [csvHeaders, ...csvRows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    response.setHeader('Content-Type', 'text/csv');
    response.setHeader('Content-Disposition', `attachment; filename="logs_${Date.now()}.csv"`);
    response.status(SUCCESS_CODE).send(csvContent);

  } catch (error) {
    console.error('Error exporting logs:', error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {}
    });
  }
};
