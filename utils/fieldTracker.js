/**
 * Field Tracker Utility
 * Helps track field changes for logging purposes
 */

class FieldTracker {
  /**
   * Track changes between old and new data
   * @param {Object} oldData - Original data before update
   * @param {Object} newData - New data after update
   * @param {Array} fieldsToTrack - Specific fields to track (optional)
   * @returns {Object} - Object containing field changes
   */
  static trackChanges(oldData, newData, fieldsToTrack = null) {
    const changes = {};
    const fieldsToCheck = fieldsToTrack || Object.keys(newData);
    
    fieldsToCheck.forEach(field => {
      if (newData.hasOwnProperty(field) && oldData[field] !== newData[field]) {
        changes[field] = {
          old_value: oldData[field],
          new_value: newData[field]
        };
      }
    });

    return Object.keys(changes).length > 0 ? changes : null;
  }

  /**
   * Track fields that were added during creation
   * @param {Object} data - Data that was added
   * @param {Object} metadata - Additional metadata (created_by, created_at, etc.)
   * @returns {Object} - Object containing all added fields
   */
  static trackAddedFields(data, metadata = {}) {
    const addedFields = { ...data };
    
    // Add metadata
    if (metadata.created_by) addedFields.created_by = metadata.created_by;
    if (metadata.created_at) addedFields.created_at = metadata.created_at;
    if (metadata.status) addedFields.status = metadata.status;
    
    return addedFields;
  }

  /**
   * Track fields that were updated
   * @param {Object} updatedData - Data that was updated
   * @param {Object} metadata - Additional metadata (updated_by, updated_at, etc.)
   * @returns {Object} - Object containing updated fields
   */
  static trackUpdatedFields(updatedData, metadata = {}) {
    const updatedFields = { ...updatedData };
    
    // Add metadata
    if (metadata.updated_by) updatedFields.updated_by = metadata.updated_by;
    if (metadata.updated_at) updatedFields.updated_at = metadata.updated_at;
    
    return updatedFields;
  }

  /**
   * Track fields that were deleted
   * @param {Object} deletedData - Data that was deleted
   * @param {Object} metadata - Additional metadata (deleted_by, deleted_at, etc.)
   * @returns {Object} - Object containing deleted fields
   */
  static trackDeletedFields(deletedData, metadata = {}) {
    const deletedFields = { ...deletedData };
    
    // Add metadata
    if (metadata.deleted_by) deletedFields.deleted_by = metadata.deleted_by;
    if (metadata.deleted_at) deletedFields.deleted_at = metadata.deleted_at;
    
    return deletedFields;
  }

  /**
   * Create comprehensive field tracking for CREATE operations
   * @param {Object} data - Data that was created
   * @param {Object} userDetails - User who performed the action
   * @returns {Object} - Complete field tracking object
   */
  static createFieldTracking(data, userDetails) {
    const addedFields = this.trackAddedFields(data, {
      created_by: userDetails.admin_id || userDetails.staff_id || userDetails.company_id,
      created_at: new Date().toISOString(),
      status: data.status || "Active"
    });

    return {
      added_fields: addedFields
    };
  }

  /**
   * Create comprehensive field tracking for UPDATE operations
   * @param {Object} oldData - Original data before update
   * @param {Object} newData - New data after update
   * @param {Object} userDetails - User who performed the action
   * @param {Array} fieldsToTrack - Specific fields to track (optional)
   * @returns {Object} - Complete field tracking object
   */
  static updateFieldTracking(oldData, newData, userDetails, fieldsToTrack = null) {
    const changes = this.trackChanges(oldData, newData, fieldsToTrack);
    const updatedFields = this.trackUpdatedFields(newData, {
      updated_by: userDetails.admin_id || userDetails.staff_id || userDetails.company_id,
      updated_at: new Date().toISOString()
    });

    return {
      changed_fields: changes,
      updated_fields: updatedFields
    };
  }

  /**
   * Create comprehensive field tracking for DELETE operations
   * @param {Object} deletedData - Data that was deleted
   * @param {Object} userDetails - User who performed the action
   * @returns {Object} - Complete field tracking object
   */
  static deleteFieldTracking(deletedData, userDetails) {
    const deletedFields = this.trackDeletedFields(deletedData, {
      deleted_by: userDetails.admin_id || userDetails.staff_id || userDetails.company_id,
      deleted_at: new Date().toISOString()
    });

    return {
      deleted_fields: deletedFields
    };
  }

  /**
   * Create field tracking for STATUS CHANGE operations
   * @param {string} oldStatus - Original status
   * @param {string} newStatus - New status
   * @param {Object} userDetails - User who performed the action
   * @returns {Object} - Complete field tracking object
   */
  static statusChangeFieldTracking(oldStatus, newStatus, userDetails) {
    const statusChange = {
      status: {
        old_value: oldStatus,
        new_value: newStatus
      }
    };
    
    const updatedFields = {
      status: newStatus,
      updated_by: userDetails.admin_id || userDetails.staff_id || userDetails.company_id,
      updated_at: new Date().toISOString()
    };

    return {
      changed_fields: statusChange,
      updated_fields: updatedFields
    };
  }

  /**
   * Create field tracking for BATCH operations
   * @param {Array} items - Array of items that were affected
   * @param {string} operation - Type of operation (activate, deactivate, delete)
   * @param {Object} userDetails - User who performed the action
   * @returns {Object} - Complete field tracking object
   */
  static batchFieldTracking(items, operation, userDetails) {
    const batchData = {
      operation: operation,
      affected_items: items,
      item_count: items.length,
      performed_by: userDetails.admin_id || userDetails.staff_id || userDetails.company_id,
      performed_at: new Date().toISOString()
    };

    return {
      batch_operation: batchData
    };
  }

  /**
   * Format field data for JSON storage
   * @param {Object} fieldData - Field tracking data
   * @returns {string} - JSON string ready for database storage
   */
  static formatForStorage(fieldData) {
    try {
      return JSON.stringify(fieldData, null, 2);
    } catch (error) {
      console.error('Error formatting field data for storage:', error);
      return JSON.stringify({ error: 'Failed to format field data' });
    }
  }

  /**
   * Parse field data from JSON storage
   * @param {string} jsonData - JSON string from database
   * @returns {Object} - Parsed field data
   */
  static parseFromStorage(jsonData) {
    try {
      return typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
    } catch (error) {
      console.error('Error parsing field data from storage:', error);
      return { error: 'Failed to parse field data' };
    }
  }
}

module.exports = FieldTracker;
