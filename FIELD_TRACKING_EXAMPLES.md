# Field Tracking Implementation Examples

## ✅ **Your Room Controller Now Tracks All Field Changes!**

### **What's Been Implemented:**

1. **CREATE Operations**: Track all fields that were added
2. **UPDATE Operations**: Track what changed (old vs new values) + all updated fields
3. **DELETE Operations**: Track complete record of what was deleted
4. **STATUS CHANGE**: Track status changes with old/new values
5. **Utility Functions**: Reusable field tracking across all controllers

### **JSON Structure in Logs Table:**

#### **1. Room Creation Log:**
```json
{
  "added_fields": {
    "room_name": "Storage Room A",
    "admin_id": 123,
    "company_id": 456,
    "staff_id": null,
    "status": "Active",
    "created_by": 123,
    "created_at": "2025-09-11T10:30:00.000Z"
  }
}
```

#### **2. Room Update Log:**
```json
{
  "changed_fields": {
    "room_name": {
      "old_value": "Storage Room A",
      "new_value": "Updated Storage Room A"
    }
  },
  "updated_fields": {
    "room_name": "Updated Storage Room A",
    "updated_by": 123,
    "updated_at": "2025-09-11T11:15:00.000Z"
  }
}
```

#### **3. Room Status Change Log:**
```json
{
  "changed_fields": {
    "status": {
      "old_value": "Active",
      "new_value": "Inactive"
    }
  },
  "updated_fields": {
    "status": "Inactive",
    "updated_by": 123,
    "updated_at": "2025-09-11T12:00:00.000Z"
  }
}
```

#### **4. Room Deletion Log:**
```json
{
  "deleted_fields": {
    "room_id": 789,
    "room_name": "Storage Room A",
    "status": "Active",
    "admin_id": 123,
    "company_id": 456,
    "staff_id": null,
    "created_at": "2025-09-10T09:00:00.000Z",
    "deleted_by": 123,
    "deleted_at": "2025-09-11T13:30:00.000Z"
  }
}
```

## **How to Use Field Tracker in Other Controllers:**

### **1. Tag Controller Example:**

```javascript
const FieldTracker = require('../../utils/fieldTracker');

// CREATE TAG
exports.createTag = async (request, response) => {
  try {
    const { tag_name, color, tag_for } = request.body;
    let getUserDetails = await commonModel.getUserDetails(request);
    
    const tagDetails = await tagModel.createTag(request.body);
    
    if (tagDetails) {
      // Track added fields
      const fieldTracking = FieldTracker.createFieldTracking({
        tag_name: tag_name,
        color: color,
        tag_for: tag_for
      }, getUserDetails);

      ActionLogModel.createActionLog({
        platform: "CMS",
        performed_by_id: getUserDetails.admin_id || getUserDetails.staff_id || getUserDetails.company_id,
        performed_by_role: getUserDetails.admin_id ? "Admin" : "User",
        performed_by_name: getUserDetails.name,
        action_type: "TAG_CREATE",
        action_performed_on_id: tagDetails.tag_id,
        action_performed_on_name: tagDetails.name,
        fields_data: fieldTracking
      });
    }
  } catch (error) {
    // Error handling
  }
};

// EDIT TAG
exports.editTag = async (request, response) => {
  try {
    const { tagId } = request.params;
    const { tag_name, color } = request.body;
    
    // Get original data
    const originalTag = await tagModel.getTag(tagId);
    
    const tagDetails = await tagModel.editTag(tagId, request.body);
    let getUserDetails = await commonModel.getUserDetails(request);
    
    if (tagDetails) {
      // Track field changes
      const fieldTracking = FieldTracker.updateFieldTracking(
        originalTag, 
        { tag_name, color }, 
        getUserDetails,
        ['tag_name', 'color'] // Only track these specific fields
      );

      ActionLogModel.createActionLog({
        platform: "CMS",
        performed_by_id: getUserDetails.admin_id || getUserDetails.staff_id || getUserDetails.company_id,
        performed_by_role: getUserDetails.admin_id ? "Admin" : "User",
        performed_by_name: getUserDetails.name,
        action_type: "TAG_UPDATE",
        action_performed_on_id: tagId,
        action_performed_on_name: tag_name,
        fields_data: fieldTracking
      });
    }
  } catch (error) {
    // Error handling
  }
};
```

### **2. Item Controller Example:**

```javascript
// ADD ITEM
exports.addItem = async (request, response) => {
  try {
    const { item_name, description, weight, volume } = request.body;
    let getUserDetails = await commonModel.getUserDetails(request);
    
    const itemDetails = await itemModel.addItem(request.body);
    
    if (itemDetails) {
      // Track added fields
      const fieldTracking = FieldTracker.createFieldTracking({
        item_name: item_name,
        description: description,
        weight: weight,
        volume: volume,
        shipment_job_id: request.body.job_id,
        stage_id: request.body.stage_id
      }, getUserDetails);

      ActionLogModel.createActionLog({
        platform: "APP",
        performed_by_id: getUserDetails.staff_id,
        performed_by_role: "User",
        performed_by_name: getUserDetails.name,
        action_type: "ITEM_ADD",
        action_performed_on_id: itemDetails.shipment_inventory_id,
        action_performed_on_name: item_name,
        fields_data: fieldTracking
      });
    }
  } catch (error) {
    // Error handling
  }
};
```

### **3. Simplified Usage with Helper Functions:**

```javascript
// For simple status changes
const fieldTracking = FieldTracker.statusChangeFieldTracking(
  "Active", 
  "Inactive", 
  getUserDetails
);

// For batch operations
const fieldTracking = FieldTracker.batchFieldTracking(
  [1, 2, 3, 4], // Array of IDs
  "deactivate", 
  getUserDetails
);
```

## **Database Query Examples:**

### **1. Find All Room Name Changes:**
```sql
SELECT * FROM app_logs 
WHERE action_type = 'ROOM_UPDATE' 
AND JSON_EXTRACT(fields_data, '$.changed_fields.room_name') IS NOT NULL;
```

### **2. Find Who Created Specific Room:**
```sql
SELECT * FROM app_logs 
WHERE action_type = 'ROOM_CREATE' 
AND action_performed_on_id = 123;
```

### **3. Find All Status Changes:**
```sql
SELECT * FROM app_logs 
WHERE JSON_EXTRACT(fields_data, '$.changed_fields.status') IS NOT NULL;
```

### **4. Get Field Changes for Specific Action:**
```sql
SELECT 
  action_type,
  action_performed_on_name,
  JSON_EXTRACT(fields_data, '$.changed_fields') as changes,
  created_at
FROM app_logs 
WHERE action_performed_on_id = 123
ORDER BY created_at DESC;
```

## **API Response Examples:**

### **When Querying Logs:**
```json
{
  "log_id": 456,
  "action_type": "ROOM_UPDATE",
  "action_performed_on_id": 123,
  "action_performed_on_name": "Storage Room A",
  "fields_data": {
    "changed_fields": {
      "room_name": {
        "old_value": "Old Room Name",
        "new_value": "New Room Name"
      }
    },
    "updated_fields": {
      "room_name": "New Room Name",
      "updated_by": 789,
      "updated_at": "2025-09-11T10:30:00.000Z"
    }
  },
  "performed_by_role": "Admin",
  "user_info": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "created_at": "2025-09-11T10:30:00.000Z"
}
```

## **Benefits:**

✅ **Complete Audit Trail**: Know exactly what was changed, when, and by whom  
✅ **Field-Level Tracking**: See old vs new values for every field  
✅ **JSON Storage**: Flexible structure that can accommodate any field changes  
✅ **Reusable Utility**: Same pattern works for all controllers  
✅ **Query Flexibility**: Easy to search and filter by specific field changes  
✅ **Compliance Ready**: Detailed logging for audit and compliance requirements  

## **Next Steps:**

1. **Apply to Other Controllers**: Use the same pattern for tags, items, shipments, etc.
2. **Add to Existing Controllers**: Update your current controllers with field tracking
3. **Create Admin Dashboard**: Build UI to view and search field changes
4. **Add Filtering**: Filter logs by specific field changes
5. **Export Functionality**: Export audit trails for compliance

Your field tracking system is now **production-ready** and provides **complete visibility** into all data changes! 🚀
