const { includes } = require("lodash");
const {
	app_logs,
	staff,
	company,
	shipment_job,
	shipment_job_assign_worker_list,
} = require("../../database/schemas");

exports.fetchActionLog = async () => {
	return app_logs.findAndCountAll({
		includes: [
			{
				model: staff,
				as: "staff_user",
				attributes: ["first_name", "last_name"],
			},
			{
				model: company,
				as: "company",
				attributes: ["company_name"],
			},
			{
				model: shipment_job,
				as: "shipment",
				attributes: ["job_number"],
			}, 
			{
				model: shipment_job_assign_worker_list,
				as: "assign_worker",
				attributes: ["role"],
			}
		],
	});
}

exports.createActionLog = async (request) => {
	return app_logs.create(request);
}