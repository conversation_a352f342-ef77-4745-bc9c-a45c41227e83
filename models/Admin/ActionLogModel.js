const { includes } = require("lodash");
const {
	app_logs,
	staff,
	admin,
	customer,
	company,
	shipment_job,
	shipment_job_assign_worker_list,
} = require("../../database/schemas");

exports.fetchActionLog = async () => {
	return app_logs.findAndCountAll({
		include: [
			{
				model: staff,
				as: "staff_user",
				attributes: ["first_name", "last_name"],
				required: false
			},
			{
				model: admin,
				as: "admin_user",
				attributes: ["first_name", "last_name"],
				required: false
			},
			{
				model: customer,
				as: "customer_user",
				attributes: ["first_name", "last_name"],
				required: false
			},
			{
				model: company,
				as: "company",
				attributes: ["company_name"],
				required: false
			},
			{
				model: shipment_job,
				as: "shipment",
				attributes: ["job_number"],
				required: false
			},
			{
				model: shipment_job_assign_worker_list,
				as: "assign_worker",
				attributes: ["role"],
				required: false
			}
		],
		order: [['created_at', 'DESC']]
	});
}

exports.createActionLog = async (request) => {
	return app_logs.create(request);
}

exports.fetchActionLogWithFilters = async (filters = {}) => {
	const { Op } = require('sequelize');
	const whereClause = {};

	// Add filters if provided
	if (filters.platform) {
		whereClause.platform = filters.platform;
	}

	if (filters.action_type) {
		whereClause.action_type = filters.action_type;
	}

	if (filters.company_id) {
		whereClause.company_id = filters.company_id;
	}

	if (filters.shipment_job_id) {
		whereClause.shipment_job_id = filters.shipment_job_id;
	}

	if (filters.user_id && filters.user_type) {
		whereClause[`${filters.user_type}_id`] = filters.user_id;
	}

	if (filters.start_date && filters.end_date) {
		whereClause.created_at = {
			[Op.between]: [filters.start_date, filters.end_date]
		};
	}

	return app_logs.findAndCountAll({
		where: whereClause,
		include: [
			{
				model: staff,
				as: "staff_user",
				attributes: ["first_name", "last_name"],
				required: false
			},
			{
				model: admin,
				as: "admin_user",
				attributes: ["first_name", "last_name"],
				required: false
			},
			{
				model: customer,
				as: "customer_user",
				attributes: ["first_name", "last_name"],
				required: false
			},
			{
				model: company,
				as: "company",
				attributes: ["company_name"],
				required: false
			},
			{
				model: shipment_job,
				as: "shipment",
				attributes: ["job_number"],
				required: false
			},
			{
				model: shipment_job_assign_worker_list,
				as: "assign_worker",
				attributes: ["role"],
				required: false
			}
		],
		order: [['created_at', 'DESC']],
		limit: filters.limit || 50,
		offset: filters.offset || 0
	});
}