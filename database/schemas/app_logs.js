"use strict";
module.exports = (sequelize, DataTypes) => {
	const app_logs = sequelize.define(
		"app_logs",
		{
			log_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			platform: {
				type: DataTypes.ENUM("APP", "CMS"),
				allowNull: false,
				comment: "Platform where action occurred - APP or CMS"
			},
			admin_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "Admin user who performed the action"
			},
			staff_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "Staff user who performed the action"
			},
			customer_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "Customer user who performed the action"
			},
			company_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "Company associated with the action"
			},
			shipment_job_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "Shipment associated with the action"
			},
			assign_job_worker_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "worker assign stage associated with the action"
			},
			shipment_inventory_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "Inventory item associated with the action"
			},
			local_shipment_stage_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "Stage where action occurred"
			},
			unit_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				comment: "Unit associated with the action"
			},
			action_type: {
				type: DataTypes.ENUM(
					"LOGIN",
					"LOGOUT",
					"STAGE_ASSIGN",
					"STAGE_REMOVE",
					"ITEM_ADD",
					"ITEM_EDIT",
					"ITEM_DELETE",
					"ITEM_ASSIGN_TO_STORAGE",
					"ITEM_REMOVE_FROM_STORAGE",
					"ITEM_REMOVE_FROM_INVENTORY",
					"UNIT_MAP",
					"UNIT_MOVE",
					"SHIPMENT_CREATE",
					"SHIPMENT_EDIT",
					"SHIPMENT_DELETE",
					"SHIPMENT_TYPE_ADD",
					"SHIPMENT_TYPE_EDIT",
					"SHIPMENT_TYPE_DELETE",
					"ROOM_ADD",
					"ROOM_EDIT",
					"ROOM_DELETE",
					"ROOM_DEACTIVATE",
					"TAG_ADD",
					"TAG_EDIT",
					"TAG_DELETE",
					"TAG_DEACTIVATE",
					"USER_ADD",
					"USER_EDIT",
					"USER_DELETE",
					"USER_DEACTIVATE",
					"CUSTOMER_ADD",
					"CUSTOMER_EDIT",
					"CUSTOMER_DELETE",
					"CUSTOMER_DEACTIVATE",
					"VIEW_ITEMS",
					"SCAN_QR",
					"MANUAL_SELECT"
				),
				allowNull: false,
				comment: "Type of action performed"
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
				comment: "Timestamp when action occurred"
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
				comment: "Timestamp when log was last updated"
			}
		},
		{
			createdAt: false,
			updatedAt: false,
		}
	);

	app_logs.associate = function (models) {
		// Foreign key associations using same key names as referenced tables
		app_logs.belongsTo(models.admin, {
			foreignKey: "admin_id",
			as: "admin_user"
		});
		app_logs.belongsTo(models.staff, {
			foreignKey: "staff_id",
			as: "staff_user"
		});
		app_logs.belongsTo(models.customer, {
			foreignKey: "customer_id",
			as: "customer_user"
		});
		app_logs.belongsTo(models.company, {
			foreignKey: "company_id",
			as: "company"
		});
		app_logs.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
			as: "shipment"
		});
		app_logs.belongsTo(models.shipment_inventory, {
			foreignKey: "shipment_inventory_id",
			as: "inventory_item"
		});
		app_logs.belongsTo(models.shipment_type_stage_for_shipment, {
			foreignKey: "local_shipment_stage_id",
			as: "shipment_stage"
		});
		app_logs.belongsTo(models.unit_list, {
			foreignKey: "unit_id",
			as: "unit"
		});
		app_logs.belongsTo(models.shipment_job_assign_worker_list, {
			foreignKey: "assign_job_worker_id",
			as: "assign_worker"
		});
	};

	return app_logs;
};
