"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable("app_logs", {
        log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false,
        },
        platform: {
          type: Sequelize.ENUM("APP", "CMS"),
          allowNull: false,
        },
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "admins",
            key: "admin_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "companies",
            key: "company_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "staffs",
            key: "staff_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        customer_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "customers",
            key: "customer_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        assign_job_worker_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_job_assign_worker_lists",
            key: "assign_job_worker_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_inventory_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_inventories",
            key: "shipment_inventory_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        local_shipment_stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_type_stage_for_shipments",
            key: "local_shipment_stage_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "unit_lists",
            key: "unit_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_room_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_rooms",
            key: "shipment_room_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        item_suggestion_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "item_suggestion",
            key: "item_suggestion_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        tag_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "tag",
            key: "tag_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_type_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_types",
            key: "shipment_type_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_type_stages",
            key: "shipment_stage_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        action_type: {
          type: Sequelize.ENUM(
            "LOGIN",
            "LOGOUT",
            
            "STAGE_ASSIGN",
            "STAGE_REMOVE",

            "ITEM_ADD",
            "ITEM_EDIT",
            "ITEM_DELETE",
            
            "ITEM_ASSIGN_TO_STORAGE",
            "ITEM_REMOVE_FROM_STORAGE",
            "ITEM_REMOVE_FROM_INVENTORY",

            "UNIT_MAP",
            "UNIT_MOVE",

            "SHIPMENT_CREATE",
            "SHIPMENT_EDIT",
            "SHIPMENT_DELETE",

            "ITEM_SUGGESTION_ADD",
            "ITEM_SUGGESTION_EDIT",
            "ITEM_SUGGESTION_DELETE",
            "ITEM_SUGGESTION_ACTIVATE",
            "ITEM_SUGGESTION_DEACTIVATE",

            "ROOM_ADD",
            "ROOM_EDIT",
            "ROOM_DELETE",
            "ROOM_ACTIVATE",
            "ROOM_DEACTIVATE",

            "SHIPMENT_TYPE_ADD",
            "SHIPMENT_TYPE_EDIT",
            "SHIPMENT_TYPE_DELETE",
            "SHIPMENT_TYPE_ACTIVATE",
            "SHIPMENT_TYPE_DEACTIVATE",

            "SHIPMENT_TYPE_STAGE_ADD",
            "SHIPMENT_TYPE_STAGE_EDIT",
            "SHIPMENT_TYPE_STAGE_DELETE",
            "SHIPMENT_TYPE_STAGE_ACTIVATE",
            "SHIPMENT_TYPE_STAGE_DEACTIVATE",

            "TAG_ADD",
            "TAG_EDIT",
            "TAG_DELETE",

            "USER_ADD",
            "USER_EDIT",
            "USER_DELETE",
            "USER_ACTIVATE",
            "USER_DEACTIVATE",

            "CUSTOMER_ADD",
            "CUSTOMER_EDIT",
            "CUSTOMER_DELETE",
            "CUSTOMER_ACTIVATE",
            "CUSTOMER_DEACTIVATE",

            "VIEW_ITEMS",
            "SCAN_QR",
            "MANUAL_SELECT"
          ),
          allowNull: false,
        },
        status: {
          type: Sequelize.ENUM,
          allowNull: true,
          values: ["Active", "Inactive"],
          defaultValue: "Active",
        },
        delete_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        created_by: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        updated_by: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        }
      });
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("app_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
