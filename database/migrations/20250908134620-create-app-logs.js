"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable("app_logs", {
        log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false,
        },
        platform: {
          type: Sequelize.ENUM("APP", "CMS"),
          allowNull: false,
        },
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "admins",
            key: "admin_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "companies",
            key: "company_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "staffs",
            key: "staff_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        customer_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "customers",
            key: "customer_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        assign_job_worker_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_job_assign_worker_lists",
            key: "assign_job_worker_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_inventory_id:{
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_inventories",
            key: "shipment_inventory_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        local_shipment_stage_id:{
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_type_stage_for_shipments",
            key: "local_shipment_stage_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "unit_lists",
            key: "unit_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        action_type: {
          type: Sequelize.ENUM(
            "LOGIN", 
            "LOGOUT", 
            "STAGE_ASSIGN", 
            "STAGE_REMOVE",
            "ITEM_ADD", 
            "ITEM_EDIT", 
            "ITEM_DELETE",
            "ITEM_ASSIGN_TO_STORAGE", 
            "ITEM_REMOVE_FROM_STORAGE",
            "ITEM_REMOVE_FROM_INVENTORY", 
            "UNIT_MAP", 
            "UNIT_MOVE",
            "SHIPMENT_CREATE", 
            "SHIPMENT_EDIT", 
            "SHIPMENT_DELETE",
            "SHIPMENT_TYPE_ADD", 
            "SHIPMENT_TYPE_EDIT", 
            "SHIPMENT_TYPE_DELETE",
            "ROOM_ADD", 
            "ROOM_EDIT", 
            "ROOM_DELETE", 
            "ROOM_DEACTIVATE",
            "TAG_ADD", 
            "TAG_EDIT", 
            "TAG_DELETE", 
            "TAG_DEACTIVATE",
            "USER_ADD", 
            "USER_EDIT", 
            "USER_DELETE", 
            "USER_DEACTIVATE",
            "CUSTOMER_ADD", 
            "CUSTOMER_EDIT", 
            "CUSTOMER_DELETE", 
            "CUSTOMER_DEACTIVATE",
            "VIEW_ITEMS", 
            "SCAN_QR", 
            "MANUAL_SELECT"
          ),
          allowNull: false,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        }
      });
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("app_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
